using System;
using System.Globalization;
using System.Windows.Data;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Converters
{
    public class DeviceTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DeviceType deviceType)
            {
                return deviceType switch
                {
                    DeviceType.Sensor => "\uE957",      // 传感器图标
                    DeviceType.Motor => "\uE945",       // 电机图标
                    DeviceType.Pump => "\uE946",        // 泵图标
                    DeviceType.Valve => "\uE947",       // 阀门图标
                    DeviceType.Controller => "\uE950",  // 控制器图标
                    DeviceType.Display => "\uE956",     // 显示器图标
                    DeviceType.Camera => "\uE722",      // 摄像头图标
                    DeviceType.Robot => "\uE99A",       // 机器人图标
                    DeviceType.Conveyor => "\uE948",    // 传送带图标
                    DeviceType.Tank => "\uE949",        // 储罐图标
                    _ => "\uE9CE"                        // 默认设备图标
                };
            }
            return "\uE9CE";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
