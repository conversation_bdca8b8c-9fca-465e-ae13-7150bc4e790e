<Window x:Class="DeviceDashboardDesigner.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:DeviceDashboardDesigner.Controls"
        Title="设备看板设计器" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="0" Background="#FFF0F0F0">
            <Button Content="新建" Style="{StaticResource DeviceButtonStyle}" Click="NewProject_Click"/>
            <Button Content="打开" Style="{StaticResource DeviceButtonStyle}" Click="OpenProject_Click"/>
            <Button Content="保存" Style="{StaticResource DeviceButtonStyle}" Click="SaveProject_Click"/>
            <Separator/>
            <Button Content="删除选中" Style="{StaticResource DeviceButtonStyle}" Click="DeleteSelected_Click"/>
            <Button Content="清空画布" Style="{StaticResource DeviceButtonStyle}" Click="ClearCanvas_Click"/>
            <Separator/>
            <TextBlock Text="网格:" VerticalAlignment="Center" Margin="5,0"/>
            <CheckBox x:Name="ShowGridCheckBox" Content="显示网格" IsChecked="True"
                      VerticalAlignment="Center" Checked="ShowGrid_Changed" Unchecked="ShowGrid_Changed"/>
        </ToolBar>

        <!-- 主工作区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 工具箱 -->
            <Border Grid.Column="0" Background="#FFF8F8F8" BorderBrush="#FFCCCCCC" BorderThickness="0,0,1,0">
                <controls:ToolboxControl x:Name="Toolbox"/>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="#FFCCCCCC"/>

            <!-- 设计画布 -->
            <Border Grid.Column="2" Background="White" BorderBrush="#FFCCCCCC" BorderThickness="1">
                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                    <controls:DesignCanvas x:Name="DesignCanvas" Width="2000" Height="1500"/>
                </ScrollViewer>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" Width="5" HorizontalAlignment="Stretch" Background="#FFCCCCCC"/>

            <!-- 属性面板 -->
            <Border Grid.Column="4" Background="#FFF8F8F8" BorderBrush="#FFCCCCCC" BorderThickness="1,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="属性面板"
                               FontWeight="Bold" FontSize="14"
                               Margin="10,10,10,5" Foreground="#FF333333"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="PropertyPanel" Margin="10">
                            <TextBlock Text="选择一个设备查看属性"
                                       Foreground="#FF666666"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="#FFF0F0F0">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="设备数量: "/>
                    <TextBlock x:Name="DeviceCountText" Text="0"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
