using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Controls
{
    public partial class DesignCanvas : UserControl
    {
        private Point _startPoint;
        private bool _isSelecting;

        public ObservableCollection<Device> Devices { get; } = new();

        public DesignCanvas()
        {
            InitializeComponent();
            Devices.CollectionChanged += Devices_CollectionChanged;
        }

        private void Devices_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (Device device in e.NewItems)
                {
                    AddDeviceToCanvas(device);
                }
            }

            if (e.OldItems != null)
            {
                foreach (Device device in e.OldItems)
                {
                    RemoveDeviceFromCanvas(device);
                }
            }
        }

        private void AddDeviceToCanvas(Device device)
        {
            var deviceControl = new DeviceControl { Device = device };

            Canvas.SetLeft(deviceControl, device.X);
            Canvas.SetTop(deviceControl, device.Y);

            DeviceCanvas.Children.Add(deviceControl);

            // 绑定位置变化
            device.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(Device.X))
                    Canvas.SetLeft(deviceControl, device.X);
                else if (e.PropertyName == nameof(Device.Y))
                    Canvas.SetTop(deviceControl, device.Y);
            };
        }

        private void RemoveDeviceFromCanvas(Device device)
        {
            var controlToRemove = DeviceCanvas.Children.OfType<DeviceControl>()
                .FirstOrDefault(c => c.Device == device);

            if (controlToRemove != null)
            {
                DeviceCanvas.Children.Remove(controlToRemove);
            }
        }

        protected override void OnDrop(DragEventArgs e)
        {
            base.OnDrop(e);

            if (e.Data.GetDataPresent(typeof(Device)))
            {
                var device = (Device)e.Data.GetData(typeof(Device));
                var position = e.GetPosition(DeviceCanvas);

                device.X = position.X;
                device.Y = position.Y;

                if (!Devices.Contains(device))
                {
                    Devices.Add(device);
                }
            }
            else if (e.Data.GetDataPresent(typeof(DeviceType)))
            {
                var deviceType = (DeviceType)e.Data.GetData(typeof(DeviceType));
                var position = e.GetPosition(DeviceCanvas);

                var newDevice = new Device
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = $"{deviceType}_{Devices.Count + 1}",
                    Type = deviceType,
                    X = position.X,
                    Y = position.Y,
                    Status = "正常",
                    Value = Random.Shared.NextDouble() * 100,
                    Unit = GetDefaultUnit(deviceType)
                };

                Devices.Add(newDevice);
            }
        }

        private string GetDefaultUnit(DeviceType deviceType)
        {
            return deviceType switch
            {
                DeviceType.Sensor => "°C",
                DeviceType.Motor => "RPM",
                DeviceType.Pump => "L/min",
                DeviceType.Valve => "%",
                DeviceType.Tank => "L",
                _ => ""
            };
        }

        protected override void OnDragOver(DragEventArgs e)
        {
            base.OnDragOver(e);
            e.Effects = DragDropEffects.Move;
            e.Handled = true;
        }

        protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonDown(e);
            _startPoint = e.GetPosition(this);
            _isSelecting = true;
            CaptureMouse();
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);

            if (_isSelecting && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPoint = e.GetPosition(this);
                var rect = new Rect(_startPoint, currentPoint);

                Canvas.SetLeft(SelectionRectangle, rect.Left);
                Canvas.SetTop(SelectionRectangle, rect.Top);
                SelectionRectangle.Width = rect.Width;
                SelectionRectangle.Height = rect.Height;
                SelectionRectangle.Visibility = Visibility.Visible;
            }
        }

        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);
            _isSelecting = false;
            SelectionRectangle.Visibility = Visibility.Collapsed;
            ReleaseMouseCapture();
        }
    }
}
