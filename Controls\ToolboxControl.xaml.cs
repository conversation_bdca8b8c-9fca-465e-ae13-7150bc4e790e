using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Controls
{
    public partial class ToolboxControl : UserControl
    {
        public ToolboxControl()
        {
            InitializeComponent();
            LoadDeviceTypes();
        }

        private void LoadDeviceTypes()
        {
            var deviceTypes = Enum.GetValues<DeviceType>().ToList();
            DeviceTypesItemsControl.ItemsSource = deviceTypes;
        }

        private void ToolboxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is DeviceType deviceType)
            {
                // 开始拖拽操作
                DragDrop.DoDragDrop(border, deviceType, DragDropEffects.Copy);
            }
        }
    }
}
