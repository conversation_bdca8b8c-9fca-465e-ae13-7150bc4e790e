using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Controls
{
    public partial class ToolboxControl : UserControl
    {
        private Point _startPoint;
        private bool _isDragging;

        public ToolboxControl()
        {
            InitializeComponent();
            LoadDeviceTypes();
        }

        private void LoadDeviceTypes()
        {
            var deviceTypes = Enum.GetValues<DeviceType>().ToList();
            DeviceTypesItemsControl.ItemsSource = deviceTypes;
        }

        private void ToolboxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border)
            {
                _startPoint = e.GetPosition(border);
                _isDragging = true;
                border.CaptureMouse();
            }
        }

        private void ToolboxItem_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && e.LeftButton == MouseButtonState.Pressed && sender is Border border)
            {
                Point currentPosition = e.GetPosition(border);
                Vector diff = currentPosition - _startPoint;

                if (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                    Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance)
                {
                    if (border.DataContext is DeviceType deviceType)
                    {
                        // 开始拖拽操作
                        DragDrop.DoDragDrop(border, deviceType, DragDropEffects.Copy);
                    }
                    _isDragging = false;
                    border.ReleaseMouseCapture();
                }
            }
        }

        private void ToolboxItem_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border)
            {
                _isDragging = false;
                border.ReleaseMouseCapture();
            }
        }
    }
}
