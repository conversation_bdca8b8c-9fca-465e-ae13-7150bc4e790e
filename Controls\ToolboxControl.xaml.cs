using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Controls
{
    public partial class ToolboxControl : UserControl
    {
        public ToolboxControl()
        {
            InitializeComponent();
            LoadDeviceTypes();
        }

        private void LoadDeviceTypes()
        {
            var deviceTypes = Enum.GetValues<DeviceType>().ToList();
            DeviceTypesItemsControl.ItemsSource = deviceTypes;
        }

        private void ToolboxItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext is DeviceType deviceType)
            {
                try
                {
                    // 直接开始拖拽操作，简化逻辑
                    DragDrop.DoDragDrop(border, deviceType, DragDropEffects.Copy);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Toolbox drag error: {ex.Message}");
                }
            }
        }

        private void ToolboxItem_MouseMove(object sender, MouseEventArgs e)
        {
            // 移除复杂的MouseMove逻辑，避免崩溃
        }

        private void ToolboxItem_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            // 简化MouseUp处理
        }
    }
}
