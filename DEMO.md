# 设备看板设计器演示

## 应用程序界面说明

### 主界面布局
1. **顶部工具栏**: 包含新建、打开、保存、删除、清空等功能按钮
2. **左侧工具箱**: 显示可拖拽的设备类型列表
3. **中央画布**: 设计区域，支持拖拽和布局设备
4. **右侧属性面板**: 显示和编辑选中设备的属性
5. **底部状态栏**: 显示当前状态和设备数量

### 功能演示步骤

#### 1. 添加设备到画布
- 从左侧工具箱选择设备类型（如传感器、电机等）
- 拖拽到中央画布的任意位置
- 设备会自动生成默认属性和图标

#### 2. 编辑设备属性
- 点击画布上的设备进行选中
- 在右侧属性面板中编辑：
  - 设备名称
  - 设备ID
  - 状态（正常/警告/故障/离线）
  - 数值和单位
  - 位置坐标

#### 3. 移动设备
- 直接拖拽画布上的设备到新位置
- 坐标会自动更新到属性面板

#### 4. 保存和加载项目
- 点击"保存"按钮保存当前设计
- 点击"打开"按钮加载已保存的项目
- 项目文件格式为JSON，扩展名为.dashboard

#### 5. 设备状态显示
- 每个设备右上角有状态指示灯：
  - 绿色：正常
  - 橙色：警告
  - 红色：故障
  - 灰色：离线

### 支持的设备类型及图标

| 设备类型 | 默认单位 | 说明 |
|---------|---------|------|
| 传感器 (Sensor) | °C | 温度、压力等传感器 |
| 电机 (Motor) | RPM | 各种电机设备 |
| 泵 (Pump) | L/min | 水泵、油泵等 |
| 阀门 (Valve) | % | 控制阀门开度 |
| 控制器 (Controller) | - | PLC、控制器等 |
| 显示器 (Display) | - | 显示屏、仪表等 |
| 摄像头 (Camera) | - | 监控摄像头 |
| 机器人 (Robot) | - | 工业机器人 |
| 传送带 (Conveyor) | - | 输送设备 |
| 储罐 (Tank) | L | 储存容器 |

### 快捷操作
- **删除设备**: 选中设备后点击"删除选中"按钮
- **清空画布**: 点击"清空画布"按钮删除所有设备
- **显示网格**: 勾选"显示网格"辅助对齐

### 文件格式
项目文件以JSON格式保存，包含所有设备的完整信息：
```json
[
  {
    "Name": "传感器_1",
    "Id": "sensor-001",
    "Type": "Sensor",
    "Status": "正常",
    "Value": 25.5,
    "Unit": "°C",
    "X": 100,
    "Y": 150,
    "Width": 100,
    "Height": 80
  }
]
```

### 扩展建议
- 可以添加更多设备类型和自定义图标
- 支持设备之间的连接线绘制
- 添加设备分组和图层功能
- 集成实时数据源
- 导出为图片或PDF格式
