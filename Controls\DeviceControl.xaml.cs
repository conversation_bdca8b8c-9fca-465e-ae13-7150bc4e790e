using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DeviceDashboardDesigner.Models;

namespace DeviceDashboardDesigner.Controls
{
    public partial class DeviceControl : UserControl
    {
        private bool _isDragging;
        private Point _startPoint;

        public DeviceControl()
        {
            InitializeComponent();
        }

        public Device Device
        {
            get => (Device)DataContext;
            set => DataContext = value;
        }

        protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonDown(e);

            if (Device != null)
            {
                _startPoint = e.GetPosition(this);
                _isDragging = true;
                CaptureMouse();
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);

            if (_isDragging && e.LeftButton == MouseButtonState.Pressed && IsMouseCaptured && Device != null)
            {
                try
                {
                    var canvas = Parent as Canvas;
                    if (canvas != null)
                    {
                        var canvasPosition = e.GetPosition(canvas);
                        // 简化位置计算
                        Device.X = Math.Max(0, Math.Min(canvasPosition.X - _startPoint.X, canvas.ActualWidth - ActualWidth));
                        Device.Y = Math.Max(0, Math.Min(canvasPosition.Y - _startPoint.Y, canvas.ActualHeight - ActualHeight));
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"DeviceControl MouseMove error: {ex.Message}");
                    StopDragging();
                }
            }
        }

        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);
            StopDragging();
        }

        private void StopDragging()
        {
            _isDragging = false;
            if (IsMouseCaptured)
            {
                ReleaseMouseCapture();
            }
        }
    }
}
