<UserControl x:Class="DeviceDashboardDesigner.Controls.DesignCanvas"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:DeviceDashboardDesigner.Controls"
             Background="White"
             AllowDrop="True">
    
    <Grid>
        <!-- 网格背景 -->
        <Canvas Name="GridCanvas" Background="Transparent">
            <Canvas.Resources>
                <DrawingBrush x:Key="GridBrush" 
                              TileMode="Tile" 
                              Viewport="0,0,20,20" 
                              ViewportUnits="Absolute">
                    <DrawingBrush.Drawing>
                        <GeometryDrawing>
                            <GeometryDrawing.Geometry>
                                <RectangleGeometry Rect="0,0,20,20"/>
                            </GeometryDrawing.Geometry>
                            <GeometryDrawing.Pen>
                                <Pen Brush="#FFE0E0E0" Thickness="0.5"/>
                            </GeometryDrawing.Pen>
                        </GeometryDrawing>
                    </DrawingBrush.Drawing>
                </DrawingBrush>
            </Canvas.Resources>
            <Rectangle Fill="{StaticResource GridBrush}" 
                       Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                       Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"/>
        </Canvas>

        <!-- 设备容器 -->
        <Canvas Name="DeviceCanvas" Background="Transparent">
            <!-- 设备控件将动态添加到这里 -->
        </Canvas>

        <!-- 选择框 -->
        <Rectangle Name="SelectionRectangle" 
                   Stroke="Blue" 
                   StrokeThickness="1" 
                   StrokeDashArray="5,3"
                   Fill="Transparent"
                   Visibility="Collapsed"/>
    </Grid>
</UserControl>
