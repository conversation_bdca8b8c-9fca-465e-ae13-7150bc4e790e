using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using DeviceDashboardDesigner.Models;
using Microsoft.Win32;

namespace DeviceDashboardDesigner
{
    public partial class MainWindow : Window
    {
        private Device? _selectedDevice;
        private string? _currentFilePath;
        private Controls.DesignCanvas? _designCanvas;
        private TextBlock? _deviceCountText;
        private TextBlock? _statusText;
        private StackPanel? _propertyPanel;

        public MainWindow()
        {
            InitializeComponent();
            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            InitializeControls();
            InitializeEvents();
            UpdateDeviceCount();
        }

        private void InitializeControls()
        {
            _designCanvas = FindName("DesignCanvas") as Controls.DesignCanvas;
            _deviceCountText = FindName("DeviceCountText") as TextBlock;
            _statusText = FindName("StatusText") as TextBlock;
            _propertyPanel = FindName("PropertyPanel") as StackPanel;
        }

        private void InitializeEvents()
        {
            if (_designCanvas != null)
            {
                _designCanvas.Devices.CollectionChanged += Devices_CollectionChanged;
                _designCanvas.DeviceSelected += OnDeviceSelected;
            }
        }

        private void OnDeviceSelected(Device device)
        {
            _selectedDevice = device;
            ShowDeviceProperties(device);
        }

        private void Devices_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            UpdateDeviceCount();
        }

        private void UpdateDeviceCount()
        {
            if (_deviceCountText != null && _designCanvas != null)
            {
                _deviceCountText.Text = _designCanvas.Devices.Count.ToString();
            }
        }

        private void NewProject_Click(object sender, RoutedEventArgs e)
        {
            if (_designCanvas?.Devices.Count > 0)
            {
                var result = MessageBox.Show("当前项目未保存，是否继续？", "新建项目",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            _designCanvas?.Devices.Clear();
            _currentFilePath = null;
            if (_statusText != null)
                _statusText.Text = "新建项目";
        }

        private void OpenProject_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "设备看板文件 (*.dashboard)|*.dashboard|所有文件 (*.*)|*.*",
                Title = "打开项目"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var json = File.ReadAllText(openFileDialog.FileName);
                    var devices = JsonSerializer.Deserialize<List<Device>>(json);

                    _designCanvas?.Devices.Clear();
                    if (devices != null && _designCanvas != null)
                    {
                        foreach (var device in devices)
                        {
                            _designCanvas.Devices.Add(device);
                        }
                    }

                    _currentFilePath = openFileDialog.FileName;
                    if (_statusText != null)
                        _statusText.Text = $"已打开: {Path.GetFileName(_currentFilePath)}";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开文件失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SaveProject_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentFilePath))
            {
                SaveAsProject();
            }
            else
            {
                SaveProject(_currentFilePath);
            }
        }

        private void SaveAsProject()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "设备看板文件 (*.dashboard)|*.dashboard|所有文件 (*.*)|*.*",
                Title = "保存项目",
                DefaultExt = "dashboard"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                SaveProject(saveFileDialog.FileName);
                _currentFilePath = saveFileDialog.FileName;
            }
        }

        private void SaveProject(string filePath)
        {
            try
            {
                if (_designCanvas != null)
                {
                    var json = JsonSerializer.Serialize(_designCanvas.Devices.ToList(), new JsonSerializerOptions
                    {
                        WriteIndented = true
                    });

                    File.WriteAllText(filePath, json);
                    if (_statusText != null)
                        _statusText.Text = $"已保存: {Path.GetFileName(filePath)}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedDevice != null && _designCanvas != null)
            {
                _designCanvas.Devices.Remove(_selectedDevice);
                _selectedDevice = null;
                ClearPropertyPanel();
                if (_statusText != null)
                    _statusText.Text = "已删除选中设备";
            }
        }

        private void ClearCanvas_Click(object sender, RoutedEventArgs e)
        {
            if (_designCanvas?.Devices.Count > 0)
            {
                var result = MessageBox.Show("确定要清空画布吗？", "清空画布",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    _designCanvas.Devices.Clear();
                    _selectedDevice = null;
                    ClearPropertyPanel();
                    if (_statusText != null)
                        _statusText.Text = "画布已清空";
                }
            }
        }

        private void ShowGrid_Changed(object sender, RoutedEventArgs e)
        {
            // 这里可以实现显示/隐藏网格的逻辑
            var showGridCheckBox = FindName("ShowGridCheckBox") as CheckBox;
            if (_statusText != null)
                _statusText.Text = showGridCheckBox?.IsChecked == true ? "网格已显示" : "网格已隐藏";
        }

        private void ClearPropertyPanel()
        {
            if (_propertyPanel != null)
            {
                _propertyPanel.Children.Clear();
                _propertyPanel.Children.Add(new TextBlock
                {
                    Text = "选择一个设备查看属性",
                    Foreground = System.Windows.Media.Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                });
            }
        }

        private void ShowDeviceProperties(Device device)
        {
            if (_propertyPanel == null) return;

            _propertyPanel.Children.Clear();

            // 设备名称
            AddPropertyEditor("设备名称", device.Name, (value) => device.Name = value);

            // 设备ID
            AddPropertyEditor("设备ID", device.Id, (value) => device.Id = value);

            // 设备类型
            AddPropertyLabel("设备类型", device.Type.ToString());

            // 状态
            AddPropertyComboBox("状态", device.Status, new[] { "正常", "警告", "故障", "离线" },
                (value) => device.Status = value);

            // 数值
            AddPropertyEditor("数值", device.Value.ToString("F2"), (value) =>
            {
                if (double.TryParse(value, out double result))
                    device.Value = result;
            });

            // 单位
            AddPropertyEditor("单位", device.Unit, (value) => device.Unit = value);

            // 位置
            AddPropertyEditor("X坐标", device.X.ToString("F0"), (value) =>
            {
                if (double.TryParse(value, out double result))
                    device.X = result;
            });

            AddPropertyEditor("Y坐标", device.Y.ToString("F0"), (value) =>
            {
                if (double.TryParse(value, out double result))
                    device.Y = result;
            });
        }

        private void AddPropertyLabel(string label, string value)
        {
            if (_propertyPanel == null) return;

            _propertyPanel.Children.Add(new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 2)
            });

            _propertyPanel.Children.Add(new TextBlock
            {
                Text = value,
                Margin = new Thickness(0, 0, 0, 5)
            });
        }

        private void AddPropertyEditor(string label, string value, Action<string> onValueChanged)
        {
            if (_propertyPanel == null) return;

            _propertyPanel.Children.Add(new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 2)
            });

            var textBox = new TextBox
            {
                Text = value,
                Margin = new Thickness(0, 0, 0, 5)
            };

            textBox.TextChanged += (s, e) => onValueChanged(textBox.Text);
            _propertyPanel.Children.Add(textBox);
        }

        private void AddPropertyComboBox(string label, string value, string[] options, Action<string> onValueChanged)
        {
            if (_propertyPanel == null) return;

            _propertyPanel.Children.Add(new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 2)
            });

            var comboBox = new ComboBox
            {
                ItemsSource = options,
                SelectedItem = value,
                Margin = new Thickness(0, 0, 0, 5)
            };

            comboBox.SelectionChanged += (s, e) =>
            {
                if (comboBox.SelectedItem is string selectedValue)
                    onValueChanged(selectedValue);
            };

            _propertyPanel.Children.Add(comboBox);
        }
    }
}
