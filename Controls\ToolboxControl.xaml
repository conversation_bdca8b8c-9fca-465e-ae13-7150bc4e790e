<UserControl x:Class="DeviceDashboardDesigner.Controls.ToolboxControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:models="clr-namespace:DeviceDashboardDesigner.Models"
             xmlns:local="clr-namespace:DeviceDashboardDesigner.Converters">
    
    <UserControl.Resources>
        <local:DeviceTypeToIconConverter x:Key="DeviceTypeToIconConverter"/>
        
        <Style x:Key="ToolboxItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFF5F5F5"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFE3F2FD"/>
                    <Setter Property="BorderBrush" Value="#FF2196F3"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具箱标题 -->
        <TextBlock Grid.Row="0" Text="设备工具箱" 
                   FontWeight="Bold" FontSize="14" 
                   Margin="5" Foreground="#FF333333"/>

        <!-- 设备类型列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl Name="DeviceTypesItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource ToolboxItemStyle}"
                                MouseLeftButtonDown="ToolboxItem_MouseLeftButtonDown">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 设备图标 -->
                                <TextBlock Grid.Column="0" 
                                           FontFamily="Segoe MDL2 Assets"
                                           FontSize="20"
                                           Foreground="#FF2196F3"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"
                                           Text="{Binding Converter={StaticResource DeviceTypeToIconConverter}}"/>

                                <!-- 设备名称 -->
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding}"
                                           FontSize="12"
                                           VerticalAlignment="Center"
                                           Foreground="#FF333333"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
