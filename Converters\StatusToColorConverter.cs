using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace DeviceDashboardDesigner.Converters
{
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "正常" => new SolidColorBrush(Colors.Green),
                    "警告" => new SolidColorBrush(Colors.Orange),
                    "故障" => new SolidColorBrush(Colors.Red),
                    "离线" => new SolidColorBrush(Colors.Gray),
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
