<UserControl x:Class="DeviceDashboardDesigner.Controls.DeviceControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:models="clr-namespace:DeviceDashboardDesigner.Models"
             xmlns:local="clr-namespace:DeviceDashboardDesigner.Converters"
             Width="100" Height="80">

    <UserControl.Resources>
        <!-- 设备图标字典 -->
        <ResourceDictionary>
            <Style x:Key="DeviceIconStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="Foreground" Value="#FF2196F3"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border BorderBrush="#FF2196F3" BorderThickness="2" CornerRadius="5" Background="White">
        <Border.Effect>
            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 设备名称 -->
            <TextBlock Grid.Row="0" Text="{Binding Name}"
                       FontWeight="Bold" FontSize="10"
                       HorizontalAlignment="Center"
                       Margin="2" Foreground="#FF333333"/>

            <!-- 设备图标和状态 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 设备图标 -->
                <TextBlock Grid.Column="0" Style="{StaticResource DeviceIconStyle}"
                           Text="{Binding Type}"/>

                <!-- 状态指示器 -->
                <Ellipse Grid.Column="1" Width="8" Height="8"
                         Margin="2" VerticalAlignment="Top"
                         Fill="Green"/>
            </Grid>

            <!-- 设备信息 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal"
                        HorizontalAlignment="Center" Margin="2">
                <TextBlock Text="{Binding Value, StringFormat=F1}"
                           FontSize="9" Foreground="#FF666666"/>
                <TextBlock Text="{Binding Unit}"
                           FontSize="9" Foreground="#FF666666" Margin="2,0,0,0"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
