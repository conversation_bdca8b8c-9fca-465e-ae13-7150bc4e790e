# 设备看板设计器

一个基于WPF的设备看板设计器，支持拖拽功能和设备信息显示。

## 功能特性

- **拖拽设计**: 从工具箱拖拽设备组件到画布上
- **设备管理**: 支持多种设备类型（传感器、电机、泵、阀门等）
- **属性编辑**: 实时编辑设备属性（名称、状态、数值等）
- **项目保存**: 支持保存和加载设备看板项目
- **网格显示**: 可选的网格背景辅助对齐
- **状态指示**: 设备状态的可视化显示

## 支持的设备类型

- 传感器 (Sensor)
- 电机 (Motor)
- 泵 (Pump)
- 阀门 (Valve)
- 控制器 (Controller)
- 显示器 (Display)
- 摄像头 (Camera)
- 机器人 (Robot)
- 传送带 (Conveyor)
- 储罐 (Tank)

## 使用方法

### 1. 添加设备
- 从左侧工具箱选择设备类型
- 拖拽到中央画布上
- 设备会自动生成默认属性

### 2. 编辑设备属性
- 点击画布上的设备选中
- 在右侧属性面板编辑设备信息
- 支持修改名称、状态、数值、单位等

### 3. 移动设备
- 直接拖拽画布上的设备到新位置
- 坐标会自动更新到属性面板

### 4. 保存项目
- 点击工具栏"保存"按钮
- 项目以JSON格式保存为.dashboard文件

### 5. 加载项目
- 点击工具栏"打开"按钮
- 选择.dashboard文件加载

## 技术架构

- **框架**: WPF (.NET 6)
- **设计模式**: MVVM
- **拖拽**: 原生WPF拖拽支持
- **数据绑定**: 双向绑定支持实时更新
- **序列化**: System.Text.Json

## 编译和运行

```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd DeviceDashboardDesigner

# 编译项目
dotnet build

# 运行应用
dotnet run
```

## 系统要求

- .NET 6.0 或更高版本
- Windows 10/11
- Visual Studio 2022 或 VS Code

## 项目结构

```
DeviceDashboardDesigner/
├── Models/                 # 数据模型
│   ├── Device.cs          # 设备模型
│   └── DeviceType.cs      # 设备类型枚举
├── Controls/              # 自定义控件
│   ├── DeviceControl.xaml # 设备控件
│   ├── DesignCanvas.xaml  # 设计画布
│   └── ToolboxControl.xaml# 工具箱控件
├── Converters/            # 值转换器
│   ├── DeviceTypeToIconConverter.cs
│   └── StatusToColorConverter.cs
├── MainWindow.xaml        # 主窗口
└── App.xaml              # 应用程序入口
```

## 扩展功能

可以进一步扩展的功能：
- 设备连接线绘制
- 更多设备类型和图标
- 设备分组功能
- 导出为图片
- 实时数据连接
- 设备动画效果
