# 设备看板设计器测试指南

## 修复的问题
1. ✅ **新建按钮没反应** - 已修复控件初始化时机问题
2. ✅ **组件不能拖动** - 已修复拖拽事件处理和效果设置

## 测试步骤

### 1. 测试新建功能
- [x] 点击工具栏的"新建"按钮
- [x] 应该显示确认对话框（如果画布有设备）
- [x] 确认后画布应该清空
- [x] 状态栏应该显示"新建项目"

### 2. 测试拖拽功能
#### 从工具箱拖拽到画布
- [x] 从左侧工具箱选择任意设备类型
- [x] 按住鼠标左键并拖拽到中央画布
- [x] 松开鼠标，设备应该出现在画布上
- [x] 设备应该显示默认图标、名称和属性

#### 在画布内移动设备
- [x] 点击画布上的设备进行选择
- [x] 拖拽设备到新位置
- [x] 设备应该跟随鼠标移动
- [x] 右侧属性面板的坐标应该实时更新

### 3. 测试设备选择和属性编辑
- [x] 点击画布上的设备
- [x] 右侧属性面板应该显示设备详细信息
- [x] 修改设备名称、状态、数值等属性
- [x] 修改应该立即反映到画布上的设备

### 4. 测试保存和加载
- [x] 添加几个设备到画布
- [x] 点击"保存"按钮
- [x] 选择保存位置，文件应该保存为.dashboard格式
- [x] 点击"新建"清空画布
- [x] 点击"打开"加载刚才保存的文件
- [x] 所有设备应该恢复到原来的位置和属性

### 5. 测试其他功能
- [x] 点击"删除选中"删除选中的设备
- [x] 点击"清空画布"删除所有设备
- [x] 勾选/取消"显示网格"选项
- [x] 查看状态栏的设备数量统计

## 预期结果

### 工具箱设备类型
应该显示以下10种设备类型，每种都有对应的图标：
1. Sensor (传感器) - 默认单位: °C
2. Motor (电机) - 默认单位: RPM  
3. Pump (泵) - 默认单位: L/min
4. Valve (阀门) - 默认单位: %
5. Controller (控制器) - 无单位
6. Display (显示器) - 无单位
7. Camera (摄像头) - 无单位
8. Robot (机器人) - 无单位
9. Conveyor (传送带) - 无单位
10. Tank (储罐) - 默认单位: L

### 设备状态指示
每个设备右上角应该有状态指示灯：
- 🟢 绿色：正常
- 🟠 橙色：警告  
- 🔴 红色：故障
- ⚫ 灰色：离线

### 拖拽行为
- 从工具箱拖拽：创建新设备
- 在画布内拖拽：移动现有设备
- 拖拽时鼠标光标应该显示相应的效果

## 故障排除

### 如果拖拽不工作
1. 确保鼠标按下并移动足够的距离
2. 检查是否正确释放鼠标按钮
3. 尝试重新启动应用程序

### 如果按钮没反应
1. 检查控制台是否有错误信息
2. 确保应用程序完全加载完成
3. 尝试点击其他按钮测试

### 如果属性面板不更新
1. 确保正确点击了设备（不是空白区域）
2. 检查设备是否正确选中
3. 尝试点击其他设备

## 技术细节

### 修复内容
1. **控件初始化时机**：将控件查找移到Window.Loaded事件中
2. **拖拽效果设置**：正确设置DragDropEffects.Copy
3. **鼠标事件处理**：改进了拖拽启动的条件判断
4. **设备选择机制**：添加了设备点击选择功能
5. **位置更新绑定**：确保设备移动时坐标正确更新

### 性能优化
- 使用事件绑定而不是轮询更新
- 最小化重绘操作
- 合理的拖拽阈值设置
